"""Jira Definition of Done (DoD) prompt templates."""

DEFINITION_OF_DONE_SYSTEM_MESSAGE = """You are an experienced software development engineer and quality assurance specialist. Your main task is to analyze development tickets (feature development, bug fixing, user story implementation, development narratives) and generate a comprehensive Definition of Done (DoD) checklist to ensure work meets completion criteria.

Your analysis should cover:
- Feature flag usage assessment
- Acceptance criteria validation  
- Test plan documentation and risk assessment
- Code review requirements
- Automated testing coverage (unit, integration, API, BDD tests)
- Technical documentation updates
- Security and privacy compliance
- Pull request size and quality checks
- Proper ticket tagging and closure procedures

Accuracy and thoroughness are critical. Ensure your DoD summary is comprehensive and actionable so that development teams can validate completion criteria effectively."""

def generate_definition_of_done_prompt(task_description: str) -> str:
    """Generate Definition of Done prompt for development tickets."""
    return f"""Scenario: As an experienced software development engineer and QA specialist, you need to analyze the given development ticket and generate a simple, clean Definition of Done (DoD) checklist.

Task Description: "{task_description}"

Instructions: Generate a Definition of Done summary following the EXACT format template below. Analyze the task description and add 3-5 specific validation criteria relevant to this particular work. Keep the format simple and flat - NO headers, NO categorization, NO complex bullet structures.

**CRITICAL FORMATTING REQUIREMENTS**:
1. Follow the exact template format shown below
2. Use simple bullet points (-) only, NO checkboxes or emoji symbols
3. Include proper line breaks between sections (Summary, DoD, Disclaimer)
4. Keep content concise but specific to the task
5. NO additional headers or categorization beyond the template
6. Add 3-5 task-specific validation criteria after the core 4 items

**EXACT OUTPUT FORMAT** (copy this structure exactly):

**Summary**: [Provide a brief 1-2 sentence summary of the task and its completion requirements]

**DoD**:

- Ensure that end-to-end customer workflows are covered \n
- Automated tests covering end-to-end customer workflows \n
- Ensure that all the fields within Jira have been filled out properly \n
- Evaluate risk / impacted areas / validation as if the code was being pushed immediately to production \n
- [Add specific validation criterion 1 based on task description]
- [Add specific validation criterion 2 based on task description]
- [Add specific validation criterion 3 based on task description]
- [Add specific validation criterion 4 based on task description - if applicable]
- [Add specific validation criterion 5 based on task description - if applicable]

**Disclaimer / Discussion notes**:

[Add 2-3 specific notes, risks, dependencies, or clarifications relevant to this particular task. Include practical considerations like rollback plans, monitoring requirements, stakeholder approvals, or areas needing additional discussion.]\n

**Examples of task-specific validation criteria**:
- Frontend feature: "UI components tested across different screen sizes and browsers"
- API change: "API documentation updated with new endpoints and response formats"
- Database change: "Database migration tested in staging environment with rollback plan"
- Bug fix: "Root cause analysis documented and preventive measures implemented"
- Performance feature: "Performance benchmarks established and validated"
- Security feature: "Security review completed and vulnerabilities addressed"

**Important**:
- Follow the template structure EXACTLY
- Use simple bullet points (-) only, NO symbols or checkboxes
- Include proper line breaks between sections
- Make validation criteria specific to the actual task described
- Keep disclaimer notes practical and task-relevant
- NO additional formatting, headers, or categorization beyond the template
"""

DEFINITION_OF_DONE_PROMPT = {
    "system_message": DEFINITION_OF_DONE_SYSTEM_MESSAGE,
    "generate_prompt": generate_definition_of_done_prompt,
    "suggestions": ["Review DoD criteria thoroughly", "Assess all quality gates", "Validate completion requirements"]
}
