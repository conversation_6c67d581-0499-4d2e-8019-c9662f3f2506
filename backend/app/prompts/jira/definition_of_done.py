"""Jira Definition of Done (DoD) prompt templates."""

DEFINITION_OF_DONE_SYSTEM_MESSAGE = """You are an experienced software development engineer and quality assurance specialist. Your main task is to analyze development tickets (feature development, bug fixing, user story implementation, development narratives) and generate a comprehensive Definition of Done (DoD) checklist to ensure work meets completion criteria.

Your analysis should cover:
- Feature flag usage assessment
- Acceptance criteria validation  
- Test plan documentation and risk assessment
- Code review requirements
- Automated testing coverage (unit, integration, API, BDD tests)
- Technical documentation updates
- Security and privacy compliance
- Pull request size and quality checks
- Proper ticket tagging and closure procedures

Accuracy and thoroughness are critical. Ensure your DoD summary is comprehensive and actionable so that development teams can validate completion criteria effectively."""

def generate_definition_of_done_prompt(task_description: str) -> str:
    """Generate Definition of Done prompt for development tickets."""
    return f"""Scenario: As an experienced software development engineer and QA specialist, you need to analyze the given development ticket and generate a comprehensive Definition of Done (DoD) checklist that covers all aspects of software delivery quality.

Task Description: "{task_description}"

Instructions: Generate a Definition of Done summary based on the task description provided. Analyze the specific task type (feature development, bug fix, enhancement, etc.) and create actionable, measurable validation criteria tailored to this specific work. Consider all aspects of software development lifecycle including feature flags, testing strategies, code quality, security, documentation, and delivery processes.

**CRITICAL FORMATTING REQUIREMENTS**:
1. Include proper line breaks between sections for readability
2. Make each validation item specific and actionable based on the task description
3. Replace generic placeholders with concrete, task-specific validation criteria
4. Ensure each checklist item is measurable and can be verified
5. Analyze the task type and scope to provide relevant validation criteria

**CONTENT REQUIREMENTS**:
- Include technology stack considerations if mentioned in the task
- Provide specific test coverage requirements based on the task scope
- Make validation criteria actionable with clear acceptance criteria
- Consider the impact and risk level of the changes described
- Include only applicable sections and make each item specific to the task

Output Format (use this exact structure with proper line breaks):

- Ensure that end-to-end customer workflows are covered (/)(x)
- Automated tests covering end-to-end customer workflows (/)(x)
- Ensure that all the fields within Jira have been filled out properly (/)(x)
- Evaluate risk / impacted areas / validation as if the code was being pushed immediately to production (/)(x)

+Disclaimer / Discussion notes:+
- Any notes from the DoD discussion should be added to the comment as well.

**Important Instructions for Content Generation**:
1. **Task-Specific Validation**: Analyze the task description and add 3-5 additional specific validation criteria relevant to the work being done. For example:
   - If it's a frontend feature: "UI components tested across different screen sizes and browsers"
   - If it's an API change: "API documentation updated with new endpoints and response formats"
   - If it's a database change: "Database migration tested in staging environment"
   - If it's a bug fix: "Root cause analysis documented and preventive measures implemented"

2. **Technology-Specific Criteria**: If the task mentions specific technologies, frameworks, or tools, include relevant validation criteria for those technologies.

3. **Risk-Based Validation**: Based on the task complexity and impact, include appropriate validation criteria:
   - High-risk changes: Additional review requirements, rollback plans, monitoring setup
   - User-facing changes: Accessibility testing, user experience validation
   - Performance-critical changes: Performance testing, load testing requirements

4. **Actionable Items**: Each validation item should be specific enough that a team member can clearly determine if it has been completed successfully. Avoid generic statements.

5. **Line Break Formatting**: Ensure proper spacing between different types of content for better readability in the final output.

Replace (/)(x) with appropriate checkboxes based on completion status. The generated content should be comprehensive yet specific to the task at hand.
"""

DEFINITION_OF_DONE_PROMPT = {
    "system_message": DEFINITION_OF_DONE_SYSTEM_MESSAGE,
    "generate_prompt": generate_definition_of_done_prompt,
    "suggestions": ["Review DoD criteria thoroughly", "Assess all quality gates", "Validate completion requirements"]
}
