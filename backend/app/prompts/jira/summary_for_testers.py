"""Jira summary for testers prompt templates."""

SUMMARY_FOR_TESTERS_SYSTEM_MESSAGE = """You are an experienced software development engineer. Your main task is to handle various development tasks (feature development, bug fixing, user story implementation, development narratives) and convert their descriptions into a specific format for testing validation. You need to read and understand the task descriptions, then determine the impacted areas and the content that testers need to validate based on the descriptions. Accuracy and detail are very important. Please ensure that your summary is as accurate and detailed as possible so that other team members can perform their work based on your summary."""

def generate_summary_for_testers_prompt(task_description: str) -> str:
    """Generate summary for testers prompt."""
    return f"""Scenario: As an accomplished developer, your role involves a multitude of development activities, including feature development, bug fixing, implementing user stories, and creating development narratives. Your task is to provide a succinct and comprehensive review based on the given task description.

Task Description: "{task_description}"

Instructions: Compile a summary in accordance with the task description provided, utilizing the markdown format as indicated below.

Output Format:
- Ensure that end-to-end customer workflows are covered (/)(x)
- Automated tests covering end-to-end customer workflows (/)(x)
- Ensure that all the fields within Jira have been filled out properly (/)(x)
- Evaluate risk / impacted areas / validation as if the code was being pushed immediately to production (/)(x)

+Disclaimer / Discussion notes:+
- Any notes from the DoD discussion should be added to the comment as well.

Note: The testing team refers to the black box testing team, who will validate the task based on the content you provide. Replace (/)(x) with appropriate checkboxes based on completion status. Add specific validation details relevant to the task description in each section.

Please ensure that all outputs follow the markdown format. If the task description does not provide sufficient information to determine the value of any field, please designate it as "Unknown" or "To be determined".
"""

SUMMARY_FOR_TESTERS_PROMPT = {
    "system_message": SUMMARY_FOR_TESTERS_SYSTEM_MESSAGE,
    "generate_prompt": generate_summary_for_testers_prompt,
    "suggestions": ["Review task description carefully", "Identify impacted areas", "Define validation steps"]
}